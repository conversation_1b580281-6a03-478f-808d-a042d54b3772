﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.RestockReporting.Models;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// Filter action that triggers automatic purchase attempts for items matching criteria
    /// </summary>
    public class RestockFilterAction : IFilterAction
    {
        public const string IDENTIFIER = "RESTOCK";

        private PurchaseExecutionService _purchaseService;
        private IItemHistoryLogger _itemHistoryLogger;

        public string DisplayName => "Restock";
        public string ActionTypeIdentifier => IDENTIFIER;

        /// <summary>
        /// Job ID for tracking purchases (optional)
        /// </summary>
        public string JobId { get; set; }

        /// <summary>
        /// Whether to enable purchase execution (for testing/debugging)
        /// </summary>
        public bool EnablePurchasing { get; set; } = true;

        public async Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            if (context?.FilterRule == null || context.SourceDataTable == null)
            {
                return FilterActionResult.CreateFailure("Invalid context provided to Restock filter");
            }

            try
            {
                var processedCount = 0;
                var purchaseAttempts = 0;
                var successfulPurchases = 0;

                // Collect all matching items first and log all items processed
                var matchingItems = new List<(DataList dataList, string filterAlias)>();

                // Determine processing mode: single-item (CurrentRow specified) vs batch (entire table)
                var rowsToProcess = GetRowsToProcess(context);

                foreach (var row in rowsToProcess)
                {
                    // Extract DataList from the row first
                    if (row["Blob"] is DataList dataList)
                    {
                        // Check if this row matches the filter criteria
                        bool filterMatched = context.FilterRule.GetEvaluator().Fit(row);

                        if (filterMatched)
                        {
                            processedCount++;
                            matchingItems.Add((dataList, context.FilterRule.Alias));
                        }

                        // Log only non-matching items here (matching items will be logged with purchase results)
                        if (!filterMatched)
                        {
                            await LogItemProcessingAsync(dataList, context.FilterRule, filterMatched);
                        }
                    }
                }

                // Queue items for checkout instead of immediate purchase attempts
                // Items will be processed by the checkout queue with 40-second intervals
                foreach (var (dataList, filterAlias) in matchingItems)
                {
                    var result = await QueueItemForCheckoutAsync(dataList, filterAlias);
                    purchaseAttempts++;

                    // Log the queuing result
                    await LogItemProcessingAsync(dataList, context.FilterRule, true, result);

                    if (result.Success)
                    {
                        successfulPurchases++;
                        // Note: UI refresh will happen when item is actually processed from queue
                    }
                }

                var message = $"Restock filter '{context.FilterRule.Alias}' processed {processedCount} matching items. " +
                             $"Purchase attempts: {purchaseAttempts}, Successful: {successfulPurchases}";

                return FilterActionResult.CreateSuccess(message, processedCount);
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Restock filter '{context.FilterRule.Alias}' failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Queues an item for checkout by setting its status to CheckoutPending
        /// The item will be processed later by the checkout queue processor
        /// </summary>
        private async Task<PurchaseExecutionResult> QueueItemForCheckoutAsync(DataList dataList, string filterAlias)
        {
            if (!EnablePurchasing)
            {
                return PurchaseExecutionResult.CreateSkipped("Purchase execution is disabled");
            }

            try
            {
                // Find the keyword that generated this DataList
                var keyword = FindKeywordByAlias(dataList.Term);

                if (keyword == null)
                {
                    return PurchaseExecutionResult.CreateSkipped($"Could not find keyword for alias: {dataList.Term}");
                }

                // Check if keyword has restock configuration
                if (string.IsNullOrEmpty(keyword.JobId) || keyword.RequiredQuantity <= 0)
                {
                    return PurchaseExecutionResult.CreateSkipped("Keyword has no restock configuration");
                }

                // Check if this item is already queued or processed to avoid duplicates
                if (dataList.ItemStatus == Data.ItemStatus.CheckoutPending ||
                    dataList.ItemStatus == Data.ItemStatus.CreatingSession ||
                    dataList.ItemStatus == Data.ItemStatus.PaymentInProgress)
                {
                    return PurchaseExecutionResult.CreateSkipped("Item is already queued or being processed");
                }

                // Store the filter alias that triggered this checkout so it can be used later
                dataList.TriggeringFilterAlias = filterAlias;

                // Set item status to CheckoutPending - this adds it to the checkout queue
                dataList.SetStatus(Data.ItemStatus.CheckoutPending);

                return PurchaseExecutionResult.CreateSuccess($"Item {dataList.ItemID} queued for checkout", 0);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("RestockEnabled is false"))
            {
                return PurchaseExecutionResult.CreateSkipped("Restock functionality is disabled. Enable RestockerEnabled in ConnectionConfig to use this feature.");
            }
            catch (Exception ex)
            {
                return PurchaseExecutionResult.CreateFailure($"Error queuing item for checkout: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Attempts to purchase an item using the purchase execution service
        /// This method is now used by the checkout queue processor
        /// </summary>
        public async Task<PurchaseExecutionResult> TryPurchaseItemAsync(DataList dataList, string filterAlias)
        {
            if (!EnablePurchasing)
            {
                return PurchaseExecutionResult.CreateSkipped("Purchase execution is disabled");
            }

            try
            {
                // Initialize services if needed
                EnsureServicesInitialized();

                // Find the keyword that generated this DataList
                var keyword = FindKeywordByAlias(dataList.Term);

                if (keyword == null)
                {
                    return PurchaseExecutionResult.CreateSkipped($"Could not find keyword for alias: {dataList.Term}");
                }

                // Check if keyword has restock configuration
                if (string.IsNullOrEmpty(keyword.JobId) || keyword.RequiredQuantity <= 0)
                {
                    return PurchaseExecutionResult.CreateSkipped("Keyword has no restock configuration");
                }

                // Execute the purchase attempt
                return await _purchaseService.TryPurchaseItemAsync(dataList, keyword, filterAlias);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("RestockEnabled is false"))
            {
                return PurchaseExecutionResult.CreateSkipped("Restock functionality is disabled. Enable RestockerEnabled in ConnectionConfig to use this feature.");
            }
            catch (Exception ex)
            {
                return PurchaseExecutionResult.CreateFailure($"Error in purchase attempt: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Finds a keyword by its alias
        /// </summary>
        private Keyword2Find FindKeywordByAlias(string alias)
        {
            if (string.IsNullOrEmpty(alias))
                return null;

            // Access the keywords collection from Form1
            var form1 = Form1.Instance;
            if (form1?.EbaySearches?.ChildrenCore == null)
                return null;

            return form1.EbaySearches.ChildrenCore.FirstOrDefault(kw => kw.Alias == alias);
        }

        /// <summary>
        /// Determines which rows to process based on the context
        /// </summary>
        private List<DataRow> GetRowsToProcess(IFilterActionContext context)
        {
            // Single-item processing: only process the specific row that triggered the filter
            if (context.CurrentRow != null)
            {
                return new List<DataRow> { context.CurrentRow };
            }

            // Batch processing: process all rows in the table (for manual filter execution)
            var rows = new List<DataRow>();
            for (int i = 0; i < context.SourceDataTable.Rows.Count; i++)
            {
                rows.Add(context.SourceDataTable.Rows[i]);
            }
            return rows;
        }

        /// <summary>
        /// Ensures that the required services are initialized
        /// </summary>
        private void EnsureServicesInitialized()
        {
            if (_purchaseService == null)
            {
                _purchaseService = new PurchaseExecutionService();
            }

            if (_itemHistoryLogger == null)
            {
                var options = ItemHistoryOptions.CreateDefault();
                _itemHistoryLogger = new FileItemHistoryLogger(options);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;

            // Validate JobId if specified
            if (!string.IsNullOrEmpty(JobId) && JobId.Length > 100)
            {
                errorMessage = "Job ID must be 100 characters or less";
                return false;
            }

            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            var data = new Dictionary<string, object>();

            if (!string.IsNullOrEmpty(JobId))
            {
                data["JobId"] = JobId;
            }

            data["EnablePurchasing"] = EnablePurchasing;

            return data;
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            if (data == null) return;

            if (data.TryGetValue("JobId", out var jobIdObj) && jobIdObj != null)
            {
                JobId = jobIdObj.ToString();
            }

            if (data.TryGetValue("EnablePurchasing", out var enablePurchasingObj) && enablePurchasingObj != null)
            {
                if (bool.TryParse(enablePurchasingObj.ToString(), out var enablePurchasing))
                {
                    EnablePurchasing = enablePurchasing;
                }
            }
        }

        /// <summary>
        /// Loads action-specific data from the filter
        /// </summary>
        public void LoadFromFilter(XFilterClass filter)
        {
            if (filter?.ActionData != null)
            {
                DeserializeActionData(filter.ActionData);
            }
        }

        /// <summary>
        /// Saves action-specific data to the filter
        /// </summary>
        public void SaveToFilter(XFilterClass filter)
        {
            if (filter != null)
            {
                filter.ActionData = SerializeActionData();
            }
        }

        /// <summary>
        /// Logs item processing context for historical analysis
        /// </summary>
        private async Task LogItemProcessingAsync(DataList dataList, XFilterClass filter, bool filterMatched, PurchaseExecutionResult purchaseResult = null)
        {
            try
            {
                // Initialize services if needed
                EnsureServicesInitialized();

                // Find the keyword that generated this DataList
                var keyword = FindKeywordByAlias(dataList.Term);

                // Create the processing context
                var context = new ItemProcessingContext
                {
                    Timestamp = DateTime.UtcNow,
                    Outcome = DataListMapper.DetermineOutcome(filterMatched, purchaseResult),
                    Reason = DataListMapper.CreateReason(filterMatched, purchaseResult),
                    ItemData = DataListMapper.ExtractItemData(dataList),
                    KeywordState = keyword != null ? KeywordSnapshot.FromKeyword2Find(keyword) : null,
                    FilterRule = DataListMapper.CreateFilterRuleContext(
                        filter.Alias,
                        filter.Expression,
                        filterMatched,
                        filterMatched ? "Filter matched - item processed" : "Filter did not match - item skipped"
                    ),
                    TransactionResult = DataListMapper.CreateTransactionResult(purchaseResult, dataList)
                };

                // Log the context asynchronously
                await _itemHistoryLogger.LogItemProcessingAsync(context);
            }
            catch (Exception ex)
            {
                // Don't let logging errors break the main processing
                Console.WriteLine($"Error logging item processing: {ex.Message}");
            }
        }

        /// <summary>
        /// Disposes of resources when the action is no longer needed
        /// </summary>
        public void Dispose()
        {
            _purchaseService?.Dispose();
            _itemHistoryLogger?.Dispose();
        }
    }

    /// <summary>
    /// UI configurator for the Restock filter action
    /// </summary>
    public class RestockFilterUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => RestockFilterAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false,
                AdditionalControlsToShow = new List<string>
                {
                    // Add any specific UI controls needed for Restock configuration
                    // For now, we'll use the basic configuration
                }
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Load any UI-specific data from the filter
            // The action-specific data is handled by the action itself
            if (filter?.ActionHandler is RestockFilterAction restockAction)
            {
                // Load any UI-specific settings if needed
                // For now, the action handles its own data loading
            }
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Save any UI-specific data to the filter
            // The action-specific data is handled by the action itself
            if (filter?.ActionHandler is RestockFilterAction restockAction)
            {
                // Save any UI-specific settings if needed
                // For now, the action handles its own data saving
                restockAction.SaveToFilter(filter);
            }
        }
    }
}
